"use client";
import React, { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import {
  ArrowLeft,
  DollarSign,
  User,
  Calendar,
  BookOpen,
  ShoppingCart,
  CheckCircle,
  Clock,
  AlertCircle,
  CreditCard,
  Eye,
  Download
} from "lucide-react";

export default function InstructorEarningsDetail() {
  const params = useParams();
  const router = useRouter();
  const [instructorData, setInstructorData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    if (params.instructor_id) {
      fetchInstructorEarnings();
    }
  }, [params.instructor_id]);

  const fetchInstructorEarnings = async () => {
    try {
      const token = localStorage.getItem("admin_token");
      const response = await fetch(`http://127.0.0.1:8000/api/admin-dashboard/instructor-earnings/${params.instructor_id}/`, {
        headers: {
          "Authorization": `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      if (response.ok) {
        const data = await response.json();
        setInstructorData(data);
      } else {
        setError("فشل في تحميل بيانات المعلم");
      }
    } catch (error) {
      console.error("Error fetching instructor earnings:", error);
      setError("حدث خطأ في تحميل البيانات");
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center space-x-2 space-x-reverse">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <span className="text-gray-600 dark:text-gray-400">جاري تحميل البيانات...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
        <div className="flex items-center">
          <AlertCircle className="w-5 h-5 text-red-500 ml-2" />
          <span className="text-red-700 dark:text-red-400">{error}</span>
        </div>
      </div>
    );
  }

  if (!instructorData) {
    return (
      <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
        <div className="flex items-center">
          <AlertCircle className="w-5 h-5 text-yellow-500 ml-2" />
          <span className="text-yellow-700 dark:text-yellow-400">لا توجد بيانات للمعلم</span>
        </div>
      </div>
    );
  }

  const { instructor, summary, courses } = instructorData;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4 space-x-reverse">
            <button
              onClick={() => router.back()}
              className="flex items-center px-3 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
            >
              <ArrowLeft className="w-5 h-5 ml-2" />
              العودة
            </button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                تفاصيل أرباح المعلم
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                {instructor.first_name} {instructor.last_name} ({instructor.username})
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2 space-x-reverse">
            <User className="w-5 h-5 text-blue-500" />
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {instructor.email}
            </span>
          </div>
        </div>
      </div>

      {/* Instructor Info */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          معلومات الدفع
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-center space-x-3 space-x-reverse">
            <CreditCard className="w-5 h-5 text-gray-400" />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">وسيلة الدفع</p>
              <p className="font-medium text-gray-900 dark:text-white">
                {instructor.payment_method || 'غير محدد'}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-3 space-x-reverse">
            <DollarSign className="w-5 h-5 text-gray-400" />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">رقم المحفظة</p>
              <p className="font-medium text-gray-900 dark:text-white">
                {instructor.wallet_number || 'غير محدد'}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">إجمالي الأرباح</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {summary.total_instructor_earnings} جنيه
              </p>
            </div>
            <div className="p-3 rounded-full bg-green-100 dark:bg-green-900/20">
              <DollarSign className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">الأرباح المعلقة</p>
              <p className="text-2xl font-bold text-orange-600">
                {summary.pending_earnings} جنيه
              </p>
            </div>
            <div className="p-3 rounded-full bg-orange-100 dark:bg-orange-900/20">
              <Clock className="w-6 h-6 text-orange-600" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">الأرباح المحولة</p>
              <p className="text-2xl font-bold text-green-600">
                {summary.transferred_earnings} جنيه
              </p>
            </div>
            <div className="p-3 rounded-full bg-green-100 dark:bg-green-900/20">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">إجمالي الطلبات</p>
              <p className="text-2xl font-bold text-blue-600">
                {summary.total_orders}
              </p>
            </div>
            <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900/20">
              <ShoppingCart className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Courses Details */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">
          تفاصيل الكورسات ({courses.length})
        </h3>
        
        <div className="space-y-6">
          {courses.map((course) => (
            <div key={course.course_id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3 space-x-reverse">
                  <BookOpen className="w-6 h-6 text-blue-500" />
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
                      {course.course_title}
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      سعر الكورس: {course.course_price} جنيه
                    </p>
                  </div>
                </div>
                <div className="text-left">
                  <p className="text-lg font-bold text-green-600">
                    {course.total_instructor_earnings.toFixed(2)} جنيه
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    إجمالي الأرباح
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <p className="text-sm text-gray-600 dark:text-gray-400">إجمالي الطلبات</p>
                  <p className="text-xl font-bold text-gray-900 dark:text-white">
                    {course.total_orders}
                  </p>
                </div>
                <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4">
                  <p className="text-sm text-orange-600 dark:text-orange-400">أرباح معلقة</p>
                  <p className="text-xl font-bold text-orange-600">
                    {course.pending_earnings.toFixed(2)} جنيه
                  </p>
                </div>
                <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
                  <p className="text-sm text-green-600 dark:text-green-400">أرباح محولة</p>
                  <p className="text-xl font-bold text-green-600">
                    {course.transferred_earnings.toFixed(2)} جنيه
                  </p>
                </div>
              </div>

              {/* Orders List */}
              <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                <h5 className="text-md font-semibold text-gray-900 dark:text-white mb-3">
                  آخر الطلبات ({course.orders.slice(0, 5).length} من {course.orders.length})
                </h5>
                <div className="space-y-2">
                  {course.orders.slice(0, 5).map((order) => (
                    <div key={order.id} className="flex items-center justify-between bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                      <div className="flex items-center space-x-3 space-x-reverse">
                        <div className={`w-3 h-3 rounded-full ${order.is_transferred ? 'bg-green-500' : 'bg-orange-500'}`}></div>
                        <div>
                          <p className="text-sm font-medium text-gray-900 dark:text-white">
                            {order.student_name}
                          </p>
                          <p className="text-xs text-gray-600 dark:text-gray-400">
                            {new Date(order.created_at).toLocaleDateString('ar-EG')}
                          </p>
                        </div>
                      </div>
                      <div className="text-left">
                        <p className="text-sm font-semibold text-gray-900 dark:text-white">
                          {order.instructor_earning.toFixed(2)} جنيه
                        </p>
                        <p className="text-xs text-gray-600 dark:text-gray-400">
                          {order.is_transferred ? 'محول' : 'معلق'}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
                {course.orders.length > 5 && (
                  <p className="text-sm text-gray-500 dark:text-gray-400 text-center mt-3">
                    +{course.orders.length - 5} طلب آخر
                  </p>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
