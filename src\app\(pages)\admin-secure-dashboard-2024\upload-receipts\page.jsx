"use client";
import React, { useEffect } from "react";
import { useRouter } from "next/navigation";
import { AlertCircle } from "lucide-react";

export default function UploadReceipts() {
  const router = useRouter();

  useEffect(() => {
    // إعادة توجيه فورية لصفحة pending-payouts
    router.replace('/admin-secure-dashboard-2024/pending-payouts');
  }, [router]);

  return (
    <div className="flex items-center justify-center h-64">
      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6 text-center">
        <AlertCircle className="w-8 h-8 text-blue-500 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-2">
          جاري إعادة التوجيه...
        </h3>
        <p className="text-blue-600 dark:text-blue-300">
          سيتم توجيهك لصفحة التحويلات المعلقة
        </p>
      </div>
    </div>
  );
}

