from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from django.db.models import Sum, Count, Q
from django.utils import timezone
from datetime import timedelta
from django.contrib.auth import get_user_model
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
import uuid
import os

from ..models import (
    Order,
    InstructorPayout,
    Course,
    Enrollment,
    User
)
from ..serializers import OrderSerializer
from ..utils.audit_logger import AuditLogger

User = get_user_model()

class IsAdminUser(permissions.BasePermission):
    """Custom permission للتحقق من أن المستخدم admin"""
    def has_permission(self, request, view):
        return request.user.is_authenticated and (request.user.is_superuser or request.user.is_staff)

class AdminDashboardStatsAPIView(APIView):
    """إحصائيات عامة للـ Admin Dashboard"""
    permission_classes = [IsAdminUser]
    
    def get(self, request):
        # تسجيل عرض Dashboard
        AuditLogger.log_action(
            admin_user=request.user,
            action_type='dashboard_view',
            description=f'عرض إحصائيات Admin Dashboard',
            request=request,
            severity='low'
        )

        # إحصائيات عامة
        total_orders = Order.objects.filter(status='completed').count()

        # حساب الإيرادات الصحيحة
        completed_orders = Order.objects.filter(status='completed')
        total_revenue = 0  # إجمالي المبيعات (الأسعار الأصلية)
        total_platform_fee = 0  # عمولة المنصة
        instructor_earnings = 0  # أرباح المعلمين

        for order in completed_orders:
            base_price = float(order.base_price) if order.base_price else float(order.amount)
            platform_fee = float(order.platform_fee) if order.platform_fee else (base_price * 0.05)

            total_revenue += base_price
            total_platform_fee += platform_fee
            instructor_earnings += (base_price - platform_fee)
        
        # إحصائيات المعلمين
        total_instructors = User.objects.filter(is_instructor=True).count()
        active_instructors = User.objects.filter(
            is_instructor=True,
            taught_courses__orders__status='completed'
        ).distinct().count()
        
        # إحصائيات الطلاب
        total_students = User.objects.filter(is_student=True).count()
        active_students = Enrollment.objects.filter(
            last_accessed__gte=timezone.now() - timedelta(days=30)
        ).values('student').distinct().count()
        
        # إحصائيات الكورسات
        total_courses = Course.objects.count()
        published_courses = Course.objects.filter(is_published=True).count()
        
        # التحويلات
        pending_payouts = InstructorPayout.objects.filter(status='pending').count()
        completed_payouts = InstructorPayout.objects.filter(status='completed').count()
        total_payouts_amount = InstructorPayout.objects.filter(
            status='completed'
        ).aggregate(Sum('amount_paid'))['amount_paid__sum'] or 0
        
        return Response({
            "revenue": {
                "total_revenue": float(total_revenue),  # إجمالي المبيعات (الأسعار الأصلية)
                "platform_fee": float(total_platform_fee),  # عمولة المنصة (5%)
                "instructor_earnings": float(instructor_earnings),  # أرباح المعلمين (95%)
            },
            "orders": {
                "total_orders": total_orders,
                "pending_orders": Order.objects.filter(status='pending').count(),
            },
            "users": {
                "total_instructors": total_instructors,
                "active_instructors": active_instructors,
                "total_students": total_students,
                "active_students": active_students,
            },
            "courses": {
                "total_courses": total_courses,
                "published_courses": published_courses,
            },
            "payouts": {
                "pending_payouts": pending_payouts,
                "completed_payouts": completed_payouts,
                "total_payouts_amount": float(total_payouts_amount),
            }
        })


class PendingPayoutsAPIView(APIView):
    """قائمة المعلمين اللي عندهم مبيعات معلقة"""
    permission_classes = [IsAdminUser]
    
    def get(self, request):
        # جلب المعلمين اللي عندهم طلبات مكتملة ومفيش لهم payout
        completed_orders = Order.objects.filter(
            status='completed',
            course__instructor__is_instructor=True
        ).exclude(
            payouts__isnull=False
        ).select_related('course__instructor', 'course')
        
        # تجميع البيانات حسب المعلم
        instructor_data = {}
        for order in completed_orders:
            instructor = order.course.instructor
            if instructor.id not in instructor_data:
                instructor_data[instructor.id] = {
                    'instructor': {
                        'id': str(instructor.id),
                        'username': instructor.username,
                        'email': instructor.email,
                        'wallet_number': instructor.wallet_number,
                        'payment_method': instructor.payment_method,
                    },
                    'orders': [],
                    'total_amount': 0,
                    'platform_fee': 0,
                    'instructor_earnings': 0,
                }
            
            # الحسابات الصحيحة:
            # order.amount = المبلغ الذي دفعه الطالب (شامل رسوم Paymob)
            # order.base_price = السعر الأصلي للكورس (بعد الخصم)
            # order.platform_fee = عمولة المنصة (5% من base_price)
            # order.paymob_fee = رسوم Paymob

            order_amount = float(order.amount)  # المبلغ المدفوع
            base_price = float(order.base_price) if order.base_price else order_amount  # السعر الأصلي
            platform_fee = float(order.platform_fee) if order.platform_fee else (base_price * 0.05)  # عمولة المنصة
            instructor_earnings = base_price - platform_fee  # أرباح المعلم = 95% من السعر الأصلي
            
            instructor_data[instructor.id]['orders'].append({
                'id': str(order.id),
                'amount': order_amount,  # المبلغ المدفوع
                'base_price': base_price,  # السعر الأصلي
                'created_at': order.created_at,
                'course_title': order.course.title,
            })
            instructor_data[instructor.id]['total_amount'] += base_price  # مجموع الأسعار الأصلية
            instructor_data[instructor.id]['platform_fee'] += platform_fee  # مجموع عمولة المنصة
            instructor_data[instructor.id]['instructor_earnings'] += instructor_earnings  # مجموع أرباح المعلم
        
        return Response({
            'pending_instructors': list(instructor_data.values()),
            'total_pending_amount': sum(data['instructor_earnings'] for data in instructor_data.values())
        })


class ProcessPayoutAPIView(APIView):
    """معالجة تحويل للمعلم - يستقبل instructor_id من URL"""
    permission_classes = [IsAdminUser]

    def post(self, request, instructor_id):
        amount = request.data.get('amount', 0)
        notes = request.data.get('notes', '')
        course_id = request.data.get('course_id')  # إضافة دعم لكورس محدد

        try:
            instructor = User.objects.get(id=instructor_id, is_instructor=True)

            # التحقق من وجود رقم محفظة
            if not instructor.wallet_number or not instructor.payment_method:
                return Response(
                    {'error': 'المعلم لم يحدد رقم محفظة أو وسيلة دفع'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # جلب الطلبات المكتملة للمعلم التي لم يتم تحويلها بعد
            completed_orders = Order.objects.filter(
                status='completed',
                course__instructor=instructor
            ).exclude(
                payouts__isnull=False  # استبعاد الطلبات التي تم تحويلها
            )

            # إذا تم تحديد كورس معين، فلتر الطلبات لهذا الكورس فقط
            if course_id:
                completed_orders = completed_orders.filter(course__id=course_id)
                notes = f"{notes} - كورس محدد: {course_id}" if notes else f"تحويل أرباح كورس محدد: {course_id}"

            if not completed_orders.exists():
                return Response(
                    {'error': 'لا توجد طلبات صالحة للتحويل'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # حساب المبالغ الصحيحة
            total_base_amount = 0
            total_platform_fee = 0
            total_instructor_earnings = 0

            for order in completed_orders:
                # استخدام base_price إذا كان متوفر، وإلا استخدم amount
                base_price = float(order.base_price) if hasattr(order, 'base_price') and order.base_price else float(order.amount)
                platform_fee = round(base_price * 0.05, 2)
                instructor_earnings = round(base_price - platform_fee, 2)

                total_base_amount += base_price
                total_platform_fee += platform_fee
                total_instructor_earnings += instructor_earnings

            # إنشاء سجل التحويل
            payout = InstructorPayout.objects.create(
                instructor=instructor,
                total_amount=total_base_amount,
                platform_fee=total_platform_fee,
                amount_paid=total_instructor_earnings,
                status='completed',  # تغيير إلى completed مباشرة
                notes=notes,
                processed_by=request.user
            )

            # ربط الطلبات بالتحويل
            payout.orders.set(completed_orders)

            # تسجيل العملية في Audit Log
            description = f'إنشاء طلب تحويل للمعلم {instructor.username} بمبلغ {total_instructor_earnings} جنيه'
            if course_id:
                course_title = completed_orders.first().course.title if completed_orders.exists() else 'غير معروف'
                description += f' - كورس: {course_title}'

            AuditLogger.log_action(
                admin_user=request.user,
                action_type='payout_created',
                description=description,
                request=request,
                target_object=payout,
                new_values={
                    'instructor': instructor.username,
                    'amount_paid': str(total_instructor_earnings),
                    'total_amount': str(total_base_amount),
                    'platform_fee': str(total_platform_fee),
                    'orders_count': completed_orders.count(),
                    'payment_method': instructor.payment_method,
                    'wallet_number': instructor.wallet_number,
                    'course_specific': bool(course_id),
                },
                additional_data={
                    'order_ids': list(completed_orders.values_list('id', flat=True)),
                    'notes': notes,
                    'course_id': course_id,
                },
                severity='high'
            )

            return Response({
                'message': 'تم إنشاء طلب التحويل بنجاح',
                'payout_id': str(payout.id),
                'amount_paid': float(total_instructor_earnings),
                'payment_method': instructor.payment_method,
                'wallet_number': instructor.wallet_number
            })

        except User.DoesNotExist:
            return Response(
                {'error': 'المعلم غير موجود'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {'error': f'حدث خطأ: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class CompletedPayoutsAPIView(APIView):
    """قائمة التحويلات المكتملة"""
    permission_classes = [IsAdminUser]
    
    def get(self, request):
        payouts = InstructorPayout.objects.filter(
            status='completed'
        ).select_related('instructor', 'processed_by').prefetch_related('orders')
        
        payout_data = []
        for payout in payouts:
            # جلب تفاصيل الكورسات المشمولة في التحويل
            courses_info = {}
            for order in payout.orders.all():
                course_id = str(order.course.id)
                if course_id not in courses_info:
                    courses_info[course_id] = {
                        'course_id': course_id,
                        'course_title': order.course.title,
                        'course_slug': order.course.slug,
                        'orders_count': 0,
                        'total_earnings': 0,
                    }

                # حساب أرباح هذا الطلب
                base_price = float(order.base_price) if order.base_price else float(order.amount)
                platform_fee = float(order.platform_fee) if order.platform_fee else (base_price * 0.05)
                instructor_earning = base_price - platform_fee

                courses_info[course_id]['orders_count'] += 1
                courses_info[course_id]['total_earnings'] += instructor_earning

            courses_list = list(courses_info.values())

            payout_data.append({
                'id': str(payout.id),
                'instructor': {
                    'id': str(payout.instructor.id),
                    'username': payout.instructor.username,
                    'email': payout.instructor.email,
                    'payment_method': payout.instructor.payment_method,
                    'wallet_number': payout.instructor.wallet_number,
                },
                'amount_paid': float(payout.amount_paid),
                'platform_fee': float(payout.platform_fee),
                'total_amount': float(payout.total_amount),
                'orders_count': payout.orders.count(),
                'courses': courses_list,  # إضافة تفاصيل الكورسات
                'receipt_image': payout.receipt.url if payout.receipt else None,
                'notes': payout.notes,
                'created_at': payout.created_at,
                'completed_at': payout.completed_at,
                'processed_by': payout.processed_by.username if payout.processed_by else None,
            })
        
        return Response({
            'completed_payouts': payout_data,
            'total_payouts': len(payout_data),
            'total_amount_paid': sum(float(p['amount_paid']) for p in payout_data)
        })


class AllOrdersAPIView(APIView):
    """جميع الطلبات للـ Admin"""
    permission_classes = [IsAdminUser]

    def get(self, request):
        orders = Order.objects.all().select_related(
            'user', 'course', 'course__instructor'
        ).order_by('-created_at')

        order_data = []
        for order in orders:
            order_data.append({
                'id': str(order.id),
                'student_name': order.user.get_full_name() if order.user.get_full_name() else order.user.username,
                'student_username': order.user.username,
                'student_email': order.user.email,
                'course_title': order.course.title if order.course else 'لا يوجد كورس',
                'instructor_name': order.course.instructor.get_full_name() if order.course and order.course.instructor.get_full_name() else (order.course.instructor.username if order.course else 'لا يوجد معلم'),
                'instructor_username': order.course.instructor.username if order.course else 'لا يوجد معلم',
                'amount': float(order.amount),
                'status': order.status,
                'created_at': order.created_at,
                'paymob_order_id': order.paymob_order_id,
            })

        return Response({
            'orders': order_data,
            'total_orders': len(order_data)
        })


class UploadReceiptAPIView(APIView):
    """رفع وصل تحويل لـ payout معين"""
    permission_classes = [IsAdminUser]

    def post(self, request, payout_id):
        try:
            # البحث عن الـ payout
            payout = InstructorPayout.objects.get(id=payout_id)

            # التحقق من وجود الصورة
            if 'receipt_image' not in request.FILES:
                return Response(
                    {'error': 'يجب إرفاق صورة الوصل'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            receipt_image = request.FILES['receipt_image']

            # التحقق من نوع الملف
            allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
            if receipt_image.content_type not in allowed_types:
                return Response(
                    {'error': 'نوع الملف غير مدعوم. يُسمح بـ JPEG, PNG, WebP فقط'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # التحقق من حجم الملف (5MB max)
            if receipt_image.size > 5 * 1024 * 1024:
                return Response(
                    {'error': 'حجم الملف كبير جداً. الحد الأقصى 5MB'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # حفظ الصورة
            file_name = f"receipt_{payout_id}_{uuid.uuid4().hex[:8]}.{receipt_image.name.split('.')[-1]}"
            file_path = default_storage.save(f"receipts/{file_name}", ContentFile(receipt_image.read()))

            # تحديث الـ payout
            old_status = payout.status
            payout.receipt = file_path
            payout.status = 'completed'
            payout.completed_at = timezone.now()
            payout.processed_by = request.user
            payout.save()

            # تسجيل العملية في Audit Log
            AuditLogger.log_action(
                admin_user=request.user,
                action_type='receipt_uploaded',
                description=f'رفع وصل تحويل للمعلم {payout.instructor.username}',
                request=request,
                target_object=payout,
                old_values={'status': old_status, 'receipt': None},
                new_values={'status': 'completed', 'receipt': file_path},
                additional_data={
                    'file_name': file_name,
                    'file_size': receipt_image.size,
                    'file_type': receipt_image.content_type,
                    'amount_paid': str(payout.amount_paid),
                },
                severity='medium'
            )

            return Response({
                'message': 'تم رفع وصل التحويل بنجاح',
                'receipt_url': default_storage.url(file_path),
                'payout_id': str(payout.id)
            })

        except InstructorPayout.DoesNotExist:
            return Response(
                {'error': 'طلب التحويل غير موجود'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {'error': f'حدث خطأ: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class AdminAuditLogAPIView(APIView):
    """API لتسجيل أعمال الإدارة"""
    permission_classes = [IsAdminUser]

    def post(self, request):
        """تسجيل عمل إداري جديد"""
        try:
            action_type = request.data.get('action_type')
            description = request.data.get('description')
            severity = request.data.get('severity', 'medium')
            additional_data = request.data.get('additional_data')

            if not action_type or not description:
                return Response(
                    {'error': 'action_type و description مطلوبان'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # تسجيل العمل
            audit_log = AuditLogger.log_action(
                admin_user=request.user,
                action_type=action_type,
                description=description,
                request=request,
                additional_data=additional_data,
                severity=severity
            )

            return Response({
                'message': 'تم تسجيل العمل بنجاح',
                'audit_log_id': str(audit_log.id)
            })

        except Exception as e:
            return Response(
                {'error': f'حدث خطأ: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def get(self, request):
        """جلب سجلات المراجعة"""
        try:
            from ..models import AdminAuditLog

            # فلاتر
            action_type = request.GET.get('action_type')
            severity = request.GET.get('severity')
            days = int(request.GET.get('days', 7))  # آخر 7 أيام افتراضياً

            # تاريخ البداية
            from datetime import timedelta
            start_date = timezone.now() - timedelta(days=days)

            # استعلام أساسي
            queryset = AdminAuditLog.objects.filter(timestamp__gte=start_date)

            # تطبيق الفلاتر
            if action_type:
                queryset = queryset.filter(action_type=action_type)
            if severity:
                queryset = queryset.filter(severity=severity)

            # ترتيب وتحديد العدد
            logs = queryset.order_by('-timestamp')[:100]  # آخر 100 سجل

            # تحويل لـ JSON
            logs_data = []
            for log in logs:
                logs_data.append({
                    'id': str(log.id),
                    'timestamp': log.timestamp.isoformat(),
                    'admin_username': log.admin_username,
                    'action_type': log.action_type,
                    'action_description': log.action_description,
                    'severity': log.severity,
                    'target_model': log.target_model,
                    'target_object_repr': log.target_object_repr,
                    'ip_address': log.ip_address,
                    'is_critical': log.is_critical,
                    'is_security_related': log.is_security_related,
                })

            return Response({
                'logs': logs_data,
                'total_count': queryset.count(),
                'filters_applied': {
                    'action_type': action_type,
                    'severity': severity,
                    'days': days,
                }
            })

        except Exception as e:
            return Response(
                {'error': f'حدث خطأ: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class InstructorEarningsDetailAPIView(APIView):
    """تفاصيل أرباح معلم معين مقسمة حسب الكورسات"""
    permission_classes = [IsAdminUser]

    def get(self, request, instructor_id):
        try:
            instructor = User.objects.get(id=instructor_id, is_instructor=True)
        except User.DoesNotExist:
            return Response(
                {'error': 'المعلم غير موجود'},
                status=status.HTTP_404_NOT_FOUND
            )

        # جلب جميع الطلبات المكتملة للمعلم
        completed_orders = Order.objects.filter(
            status='completed',
            course__instructor=instructor
        ).select_related('course').order_by('-created_at')

        # تجميع البيانات حسب الكورس
        course_earnings = {}
        total_earnings = 0
        total_platform_fee = 0
        total_base_amount = 0

        for order in completed_orders:
            course_id = str(order.course.id)
            course_title = order.course.title

            # حساب المبالغ
            base_price = float(order.base_price) if order.base_price else float(order.amount)
            platform_fee = float(order.platform_fee) if order.platform_fee else (base_price * 0.05)
            instructor_earning = base_price - platform_fee

            if course_id not in course_earnings:
                course_earnings[course_id] = {
                    'course_id': course_id,
                    'course_title': course_title,
                    'course_slug': order.course.slug,
                    'course_price': float(order.course.price),
                    'orders': [],
                    'total_orders': 0,
                    'total_base_amount': 0,
                    'total_platform_fee': 0,
                    'total_instructor_earnings': 0,
                    'pending_orders': [],  # الطلبات التي لم يتم تحويلها بعد
                    'transferred_orders': [],  # الطلبات التي تم تحويلها
                }

            # إضافة تفاصيل الطلب
            order_data = {
                'id': str(order.id),
                'amount': float(order.amount),
                'base_price': base_price,
                'platform_fee': platform_fee,
                'instructor_earning': instructor_earning,
                'created_at': order.created_at,
                'student_name': order.user.username if order.user else 'غير معروف',
                'is_transferred': order.payouts.exists(),  # هل تم تحويل هذا الطلب
            }

            course_earnings[course_id]['orders'].append(order_data)
            course_earnings[course_id]['total_orders'] += 1
            course_earnings[course_id]['total_base_amount'] += base_price
            course_earnings[course_id]['total_platform_fee'] += platform_fee
            course_earnings[course_id]['total_instructor_earnings'] += instructor_earning

            # تصنيف الطلبات حسب حالة التحويل
            if order.payouts.exists():
                course_earnings[course_id]['transferred_orders'].append(order_data)
            else:
                course_earnings[course_id]['pending_orders'].append(order_data)

            # إضافة للإجمالي العام
            total_base_amount += base_price
            total_platform_fee += platform_fee
            total_earnings += instructor_earning

        # تحويل القاموس إلى قائمة مرتبة حسب الأرباح
        courses_list = list(course_earnings.values())
        courses_list.sort(key=lambda x: x['total_instructor_earnings'], reverse=True)

        # حساب الأرباح المعلقة والمحولة
        pending_earnings = 0
        transferred_earnings = 0

        for course in courses_list:
            course_pending = sum(order['instructor_earning'] for order in course['pending_orders'])
            course_transferred = sum(order['instructor_earning'] for order in course['transferred_orders'])

            course['pending_earnings'] = course_pending
            course['transferred_earnings'] = course_transferred

            pending_earnings += course_pending
            transferred_earnings += course_transferred

        # معلومات المعلم
        instructor_info = {
            'id': str(instructor.id),
            'username': instructor.username,
            'email': instructor.email,
            'first_name': instructor.first_name,
            'last_name': instructor.last_name,
            'payment_method': instructor.payment_method,
            'wallet_number': instructor.wallet_number,
        }

        # إحصائيات عامة
        summary = {
            'total_courses': len(courses_list),
            'total_orders': completed_orders.count(),
            'total_base_amount': round(total_base_amount, 2),
            'total_platform_fee': round(total_platform_fee, 2),
            'total_instructor_earnings': round(total_earnings, 2),
            'pending_earnings': round(pending_earnings, 2),
            'transferred_earnings': round(transferred_earnings, 2),
        }

        return Response({
            'instructor': instructor_info,
            'summary': summary,
            'courses': courses_list,
        })
