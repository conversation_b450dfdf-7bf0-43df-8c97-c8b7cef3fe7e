"use client";
import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  DollarSign,
  User,
  Calendar,
  CreditCard,
  AlertCircle,
  CheckCircle,
  Clock,
  Eye,
  Send,
  Upload,
  X,
  Image as ImageIcon
} from "lucide-react";

export default function PendingPayouts() {
  const router = useRouter();
  const [pendingPayouts, setPendingPayouts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [processingPayout, setProcessingPayout] = useState(null);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [selectedPayout, setSelectedPayout] = useState(null);
  const [uploadingFile, setUploadingFile] = useState(null);
  const [previewImage, setPreviewImage] = useState(null);
  const [uploadProgress, setUploadProgress] = useState(0);

  useEffect(() => {
    fetchPendingPayouts();
  }, []);

  const fetchPendingPayouts = async () => {
    try {
      const token = localStorage.getItem("admin_token");
      const response = await fetch("http://127.0.0.1:8000/api/admin-dashboard/pending-payouts/", {
        headers: {
          "Authorization": `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      if (response.ok) {
        const data = await response.json();
        setPendingPayouts(data.pending_instructors || []);
      } else {
        setError("فشل في تحميل المبيعات المعلقة");
      }
    } catch (error) {
      console.error("Error fetching pending payouts:", error);
      setError("حدث خطأ في تحميل البيانات");
    } finally {
      setLoading(false);
    }
  };

  const handleProcessPayout = async (instructorId, amount) => {
    if (!confirm(`هل أنت متأكد من تحويل ${amount.toFixed(2)} جنيه للمعلم؟`)) {
      return;
    }

    setProcessingPayout(instructorId);
    try {
      const token = localStorage.getItem("admin_token");
      const response = await fetch(`http://127.0.0.1:8000/api/admin-dashboard/process-payout/${instructorId}/`, {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          amount: amount,
          notes: "تحويل من Admin Dashboard"
        }),
      });

      if (response.ok) {
        const data = await response.json();

        alert(`تم إنشاء طلب التحويل بنجاح!\nالمبلغ: ${data.amount_paid} جنيه\nوسيلة الدفع: ${data.payment_method}\nرقم المحفظة: ${data.wallet_number}`);

        // إعادة تحميل البيانات لإظهار التحديث
        fetchPendingPayouts();
      } else {
        // التحقق من نوع الـ response
        const contentType = response.headers.get("content-type");
        if (contentType && contentType.includes("application/json")) {
          const errorData = await response.json();
          alert(`فشل في إنشاء طلب التحويل: ${errorData.error || 'خطأ غير معروف'}`);
        } else {
          // إذا كان الـ response HTML (مثل صفحة خطأ)
          const errorText = await response.text();
          console.error("Server returned HTML instead of JSON:", errorText);
          alert("حدث خطأ في الخادم. يرجى المحاولة مرة أخرى.");
        }
      }
    } catch (error) {
      console.error("Error processing payout:", error);
      alert("حدث خطأ في معالجة التحويل");
    } finally {
      setProcessingPayout(null);
    }
  };

  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // التحقق من نوع الملف
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      alert('نوع الملف غير مدعوم. يُسمح بـ JPEG, PNG, WebP فقط');
      return;
    }

    // التحقق من حجم الملف (5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert('حجم الملف كبير جداً. الحد الأقصى 5MB');
      return;
    }

    setUploadingFile(file);

    // إنشاء preview للصورة
    const reader = new FileReader();
    reader.onload = (e) => {
      setPreviewImage(e.target.result);
    };
    reader.readAsDataURL(file);
  };

  const handleUploadReceipt = async () => {
    if (!uploadingFile || !selectedPayout) return;

    const formData = new FormData();
    formData.append('receipt_image', uploadingFile);

    try {
      setUploadProgress(0);
      const token = localStorage.getItem("admin_token");

      const xhr = new XMLHttpRequest();

      // تتبع التقدم
      xhr.upload.addEventListener('progress', (e) => {
        if (e.lengthComputable) {
          const progress = (e.loaded / e.total) * 100;
          setUploadProgress(progress);
        }
      });

      // إعداد الطلب
      xhr.open('POST', `http://127.0.0.1:8000/api/admin-dashboard/upload-receipt/${selectedPayout.payout_id}/`);
      xhr.setRequestHeader('Authorization', `Bearer ${token}`);

      xhr.onload = function() {
        if (xhr.status === 200) {
          const response = JSON.parse(xhr.responseText);
          alert('تم رفع وصل التحويل بنجاح!');

          // إعادة تحميل البيانات
          fetchPendingPayouts();

          // إعادة تعيين الحالة
          setShowUploadModal(false);
          setSelectedPayout(null);
          setUploadingFile(null);
          setPreviewImage(null);
          setUploadProgress(0);
        } else {
          const errorResponse = JSON.parse(xhr.responseText);
          alert(`فشل في رفع الوصل: ${errorResponse.error || 'خطأ غير معروف'}`);
        }
      };

      xhr.onerror = function() {
        alert('حدث خطأ في رفع الملف');
        setUploadProgress(0);
      };

      xhr.send(formData);

    } catch (error) {
      console.error("Upload error:", error);
      alert('حدث خطأ في رفع الملف');
      setUploadProgress(0);
    }
  };

  const cancelUpload = () => {
    setShowUploadModal(false);
    setSelectedPayout(null);
    setUploadingFile(null);
    setPreviewImage(null);
    setUploadProgress(0);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center space-x-2 space-x-reverse">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <span className="text-gray-600 dark:text-gray-400">جاري تحميل المبيعات المعلقة...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
        <div className="flex items-center">
          <AlertCircle className="w-5 h-5 text-red-500 ml-2" />
          <span className="text-red-700 dark:text-red-400">{error}</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
              المبيعات المعلقة
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              قائمة المعلمين الذين لديهم مبيعات معلقة تحتاج لتحويل
            </p>
          </div>
          <div className="flex items-center space-x-2 space-x-reverse">
            <Clock className="w-5 h-5 text-orange-500" />
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {pendingPayouts.length} معلم معلق
            </span>
          </div>
        </div>
      </div>

      {/* Upload Modal */}
      {showUploadModal && selectedPayout && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              {/* Modal Header */}
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  رفع وصل التحويل - {selectedPayout.instructor_name || selectedPayout.instructor_username || 'مستخدم غير معروف'}
                </h3>
                <button
                  onClick={cancelUpload}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>

              {/* Payout Info */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6">
                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">المبلغ المستحق</p>
                    <p className="text-lg font-semibold text-gray-900 dark:text-white">
                      {parseFloat(selectedPayout.total_earnings).toFixed(2)} جنيه
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">وسيلة الدفع</p>
                    <p className="text-lg font-semibold text-gray-900 dark:text-white">
                      {selectedPayout.payment_method || 'غير محدد'}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">رقم المحفظة</p>
                    <p className="text-lg font-semibold text-gray-900 dark:text-white">
                      {selectedPayout.wallet_number || 'غير محدد'}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">عدد الطلبات</p>
                    <p className="text-lg font-semibold text-gray-900 dark:text-white">
                      {selectedPayout.orders_count} طلب
                    </p>
                  </div>
                </div>

                {/* Courses Details */}
                {selectedPayout.courses && selectedPayout.courses.length > 0 && (
                  <div className="border-t border-gray-200 dark:border-gray-600 pt-4">
                    <p className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                      الكورسات المشمولة ({selectedPayout.courses.length})
                    </p>
                    <div className="space-y-2">
                      {selectedPayout.courses.map((course, index) => (
                        <div key={course.course_id} className="flex items-center justify-between bg-white dark:bg-gray-800 rounded-lg p-3 border border-gray-200 dark:border-gray-600">
                          <div className="flex-1">
                            <p className="font-medium text-gray-900 dark:text-white text-sm">
                              {course.course_title}
                            </p>
                            <p className="text-xs text-gray-600 dark:text-gray-400">
                              {course.orders_count} طلب
                            </p>
                          </div>
                          <div className="text-left">
                            <p className="font-semibold text-gray-900 dark:text-white text-sm">
                              {course.total_earnings.toFixed(2)} جنيه
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* File Upload */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-900 dark:text-white mb-2">
                  اختر وصل التحويل
                </label>
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleFileSelect}
                  className="block w-full text-sm text-gray-500 dark:text-gray-400 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 dark:file:bg-blue-900/20 dark:file:text-blue-400"
                />
              </div>

              {/* Image Preview */}
              {previewImage && (
                <div className="mb-6">
                  <p className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                    معاينة الوصل
                  </p>
                  <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4">
                    <img
                      src={previewImage}
                      alt="Receipt preview"
                      className="max-w-full h-auto max-h-64 mx-auto rounded-lg"
                    />
                  </div>
                </div>
              )}

              {/* Upload Progress */}
              {uploadProgress > 0 && uploadProgress < 100 && (
                <div className="mb-6">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-600 dark:text-gray-400">جاري الرفع...</span>
                    <span className="text-sm text-gray-600 dark:text-gray-400">{Math.round(uploadProgress)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${uploadProgress}%` }}
                    ></div>
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex justify-end space-x-3 space-x-reverse">
                <button
                  onClick={cancelUpload}
                  className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors"
                >
                  إلغاء
                </button>
                <button
                  onClick={handleUploadReceipt}
                  disabled={!uploadingFile || uploadProgress > 0}
                  className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg transition-colors flex items-center"
                >
                  <Upload className="w-4 h-4 ml-2" />
                  رفع الوصل
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">إجمالي المعلمين</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{pendingPayouts.length}</p>
            </div>
            <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900/20">
              <User className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">إجمالي المبلغ المعلق</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {pendingPayouts.reduce((sum, instructorData) => sum + (instructorData.instructor_earnings || 0), 0).toFixed(2)} جنيه
              </p>
            </div>
            <div className="p-3 rounded-full bg-orange-100 dark:bg-orange-900/20">
              <DollarSign className="w-6 h-6 text-orange-600" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">إجمالي الطلبات</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {pendingPayouts.reduce((sum, instructorData) => sum + (instructorData.orders?.length || 0), 0)}
              </p>
            </div>
            <div className="p-3 rounded-full bg-green-100 dark:bg-green-900/20">
              <CreditCard className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Pending Payouts List */}
      {pendingPayouts.length === 0 ? (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-12 text-center">
          <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            لا توجد مبيعات معلقة
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            جميع المعلمين تم تحويل مستحقاتهم
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {pendingPayouts.map((instructorData, index) => (
            <div
              key={instructorData.instructor?.id || index}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  {/* Instructor Info */}
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center">
                      <span className="text-white font-semibold text-lg">
                        {(instructorData.instructor?.username || 'U').charAt(0).toUpperCase()}
                      </span>
                    </div>
                    <div className="mr-4">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                        {instructorData.instructor?.username || 'مستخدم غير معروف'}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {instructorData.instructor?.email || 'بريد غير متوفر'}
                      </p>
                    </div>
                  </div>

                  {/* Payment Info */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">إجمالي المبيعات</p>
                      <p className="text-lg font-semibold text-gray-900 dark:text-white">
                        {(instructorData.total_amount || 0).toFixed(2)} جنيه
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">عمولة المنصة (5%)</p>
                      <p className="text-lg font-semibold text-red-600">
                        {(instructorData.platform_fee || 0).toFixed(2)} جنيه
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">مستحقات المعلم</p>
                      <p className="text-lg font-semibold text-green-600">
                        {(instructorData.instructor_earnings || 0).toFixed(2)} جنيه
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">عدد الطلبات</p>
                      <p className="text-lg font-semibold text-blue-600">
                        {instructorData.orders?.length || 0} طلب
                      </p>
                    </div>
                  </div>

                  {/* Payment Method */}
                  <div className="mb-4">
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">وسيلة الدفع</p>
                    <div className="flex items-center">
                      <CreditCard className="w-4 h-4 text-gray-500 ml-2" />
                      <span className="text-sm text-gray-900 dark:text-white">
                        {instructorData.instructor?.payment_method || "غير محدد"} - {instructorData.instructor?.wallet_number || "غير محدد"}
                      </span>
                    </div>
                  </div>

                  {/* Recent Orders */}
                  <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                    <p className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                      آخر الطلبات ({(instructorData.orders || []).slice(0, 3).length} من {instructorData.orders?.length || 0})
                    </p>
                    <div className="space-y-2">
                      {(instructorData.orders || []).slice(0, 3).map((order, orderIndex) => (
                        <div key={order.id || orderIndex} className="flex items-center justify-between text-sm">
                          <span className="text-gray-600 dark:text-gray-400">
                            {order.course_title || 'كورس غير معروف'}
                          </span>
                          <div className="flex items-center space-x-2 space-x-reverse">
                            <span className="text-gray-900 dark:text-white font-medium">
                              {(order.amount || 0).toFixed(2)} جنيه
                            </span>
                            <span className="text-gray-500">
                              {order.created_at ? new Date(order.created_at).toLocaleDateString('ar-EG') : 'تاريخ غير متوفر'}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-col space-y-2 mr-4">
                  <button
                    onClick={() => handleProcessPayout(instructorData.instructor?.id, instructorData.instructor_earnings || 0)}
                    disabled={processingPayout === instructorData.instructor?.id}
                    className="flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
                  >
                    {processingPayout === instructorData.instructor?.id ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                        جاري التحويل...
                      </>
                    ) : (
                      <>
                        <Send className="w-4 h-4 ml-2" />
                        تحويل المبلغ
                      </>
                    )}
                  </button>

                  <button
                    onClick={() => {
                      setSelectedPayout(instructorData);
                      setShowUploadModal(true);
                    }}
                    className="flex items-center px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded-lg transition-colors"
                  >
                    <ImageIcon className="w-4 h-4 ml-2" />
                    رفع وصل التحويل
                  </button>

                  <button
                    onClick={() => router.push(`/admin-secure-dashboard-2024/instructor-earnings/${instructorData.instructor?.id}`)}
                    className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                  >
                    <Eye className="w-4 h-4 ml-2" />
                    عرض التفاصيل
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
